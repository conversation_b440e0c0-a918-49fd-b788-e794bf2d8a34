import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { RuleManagementService } from '@/lib/services/rule-management'
import { handleApiError, withErrorContext } from '@/lib/utils/error-logger'
import { z } from 'zod'

// Request validation schema
const validateConditionSchema = z.object({
  linkId: z.string().min(1, 'Link ID is required'),
  type: z.enum(['referrer', 'location', 'device', 'time', 'schedule']),
  priority: z.number().int().min(0).max(1000),
  isActive: z.boolean().default(true),
  rules: z.record(z.any()),
  action: z.object({
    type: z.enum(['show', 'hide', 'redirect']),
    value: z.string().optional(),
    alternateTitle: z.string().optional(),
    alternateIcon: z.string().optional(),
    metadata: z.record(z.any()).optional()
  }),
  validationLevel: z.enum(['strict', 'moderate', 'lenient']).default('moderate'),
  checkConflicts: z.boolean().default(true),
  includeOptimizationSuggestions: z.boolean().default(true)
})

// Error codes for consistent error handling
export const ERROR_CODES = {
  UNAUTHORIZED: 'UNAUTHORIZED',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  SERVER_ERROR: 'SERVER_ERROR'
} as const

type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES]

interface ApiError {
  error: string
  code: ErrorCode
  details?: string[]
  timestamp: string
}

/**
 * POST /api/rules/conditions/validate
 * Validate a condition configuration without creating it
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      const error: ApiError = {
        error: 'Authentication required',
        code: ERROR_CODES.UNAUTHORIZED,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 401 })
    }

    const body = await request.json()
    const validatedData = validateConditionSchema.parse(body)

    // Initialize rule management service
    const ruleService = RuleManagementService.getInstance({
      validationLevel: validatedData.validationLevel,
      enableOptimization: validatedData.includeOptimizationSuggestions
    })

    // Perform comprehensive validation
    const validationResult = await ruleService.validateRuleComprehensive(
      {
        linkId: validatedData.linkId,
        type: validatedData.type,
        priority: validatedData.priority,
        isActive: validatedData.isActive,
        rules: validatedData.rules,
        action: validatedData.action
      },
      {
        linkId: validatedData.linkId,
        validationLevel: validatedData.validationLevel
      }
    )

    // Check for conflicts if requested
    let conflictResult = null
    if (validatedData.checkConflicts) {
      conflictResult = await ruleService.detectConflicts(validatedData.linkId)
    }

    // Get optimization suggestions if requested
    let optimizationSuggestions = null
    if (validatedData.includeOptimizationSuggestions) {
      const optimizationResult = await ruleService.optimizeRules(validatedData.linkId, {
        dryRun: true,
        includeAnalysis: true
      })
      optimizationSuggestions = optimizationResult.suggestions
    }

    return NextResponse.json({
      valid: validationResult.isValid,
      validation: {
        errors: validationResult.errors,
        warnings: validationResult.warnings,
        semanticIssues: validationResult.semanticIssues,
        performanceWarnings: validationResult.performanceWarnings,
        securityConcerns: validationResult.securityConcerns,
        compatibilityIssues: validationResult.compatibilityIssues,
        suggestions: validationResult.suggestions
      },
      conflicts: conflictResult ? {
        hasConflicts: conflictResult.hasConflicts,
        conflicts: conflictResult.conflicts,
        warnings: conflictResult.warnings,
        suggestions: conflictResult.suggestions
      } : null,
      optimization: optimizationSuggestions ? {
        suggestions: optimizationSuggestions,
        potentialImprovements: optimizationSuggestions.length
      } : null,
      metadata: {
        validationLevel: validatedData.validationLevel,
        checkConflicts: validatedData.checkConflicts,
        includeOptimizationSuggestions: validatedData.includeOptimizationSuggestions
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      const validationError: ApiError = {
        error: 'Invalid request data',
        code: ERROR_CODES.VALIDATION_ERROR,
        details: error.errors.map(e => `${e.path.join('.')}: ${e.message}`),
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(validationError, { status: 400 })
    }

    return handleApiError(error, 'condition validation', withErrorContext(request))
  }
}

/**
 * GET /api/rules/conditions/validate
 * Get validation rules and schema information
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      const error: ApiError = {
        error: 'Authentication required',
        code: ERROR_CODES.UNAUTHORIZED,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 401 })
    }

    // Return validation schema and rules information
    return NextResponse.json({
      schema: {
        conditionTypes: ['referrer', 'location', 'device', 'time', 'schedule'],
        actionTypes: ['show', 'hide', 'redirect'],
        priorityRange: { min: 0, max: 1000 },
        validationLevels: ['strict', 'moderate', 'lenient']
      },
      rules: {
        referrer: {
          required: ['domains'],
          optional: ['matchType', 'caseSensitive'],
          description: 'Match based on referring domain'
        },
        location: {
          required: ['countries'],
          optional: ['regions', 'cities', 'excludeCountries'],
          description: 'Match based on visitor location'
        },
        device: {
          required: ['deviceTypes'],
          optional: ['platforms', 'browsers'],
          description: 'Match based on device characteristics'
        },
        time: {
          required: [],
          optional: ['daysOfWeek', 'startTime', 'endTime', 'timezone', 'dateRange'],
          description: 'Match based on time conditions'
        },
        schedule: {
          required: ['scheduleStart', 'timezone'],
          optional: ['scheduleEnd', 'recurring'],
          description: 'Match based on scheduled time periods'
        }
      },
      validationChecks: {
        syntax: 'Validates rule structure and required fields',
        semantic: 'Checks for logical consistency and conflicts',
        performance: 'Identifies potentially slow rule configurations',
        security: 'Prevents malicious rule configurations',
        compatibility: 'Ensures compatibility with existing rules'
      },
      conflictTypes: {
        unreachable: 'Rules that can never be triggered due to higher priority rules',
        contradictory: 'Rules with conflicting actions for the same conditions',
        circular: 'Rules that create circular dependencies'
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    return handleApiError(error, 'validation schema retrieval', withErrorContext(request))
  }
}
